  /* Default Notifications */
  function default_noti() {
	Lobibox.notify('default', {
		pauseDelayOnHover: true,
		continueDelayOnInactiveTab: false,
		position: 'top right',
		msg: 'Lorem ipsum dolor sit amet hears farmer indemnity inherent.'
	});
}

function info_noti() {
	Lobibox.notify('info', {
		pauseDelayOnHover: true,
		continueDelayOnInactiveTab: false,
		position: 'top right',
		icon: 'bi bi-info-circle',
		msg: 'Lorem ipsum dolor sit amet hears farmer indemnity inherent.'
	});
}

function warning_noti() {
	Lobibox.notify('warning', {
		pauseDelayOnHover: true,
		continueDelayOnInactiveTab: false,
		position: 'top right',
		icon: 'bi bi-exclamation-triangle',
		msg: 'Lorem ipsum dolor sit amet hears farmer indemnity inherent.'
	});
}

function error_noti() {
	Lobibox.notify('error', {
		pauseDelayOnHover: true,
		continueDelayOnInactiveTab: false,
		position: 'top right',
		icon: 'bi bi-x-circle',
		msg: 'Lorem ipsum dolor sit amet hears farmer indemnity inherent.'
	});
}

function success_noti() {
	Lobibox.notify('success', {
		pauseDelayOnHover: true,
		continueDelayOnInactiveTab: false,
		position: 'top right',
		icon: 'bi bi-check2-circle',
		msg: 'Lorem ipsum dolor sit amet hears farmer indemnity inherent.'
	});
}
/* Rounded corners Notifications */
function round_default_noti() {
	Lobibox.notify('default', {
		pauseDelayOnHover: true,
		size: 'mini',
		rounded: true,
		delayIndicator: false,
		continueDelayOnInactiveTab: false,
		position: 'top right',
		msg: 'Lorem ipsum dolor sit amet hears farmer indemnity inherent.'
	});
}

function round_info_noti() {
	Lobibox.notify('info', {
		pauseDelayOnHover: true,
		size: 'mini',
		rounded: true,
		icon: 'bi bi-info-circle',
		delayIndicator: false,
		continueDelayOnInactiveTab: false,
		position: 'top right',
		msg: 'Lorem ipsum dolor sit amet hears farmer indemnity inherent.'
	});
}

function round_warning_noti() {
	Lobibox.notify('warning', {
		pauseDelayOnHover: true,
		size: 'mini',
		rounded: true,
		delayIndicator: false,
		icon: 'bi bi-exclamation-triangle',
		continueDelayOnInactiveTab: false,
		position: 'top right',
		msg: 'Lorem ipsum dolor sit amet hears farmer indemnity inherent.'
	});
}

function round_error_noti() {
	Lobibox.notify('error', {
		pauseDelayOnHover: true,
		size: 'mini',
		rounded: true,
		delayIndicator: false,
		icon: 'bi bi-x-circle',
		continueDelayOnInactiveTab: false,
		position: 'top right',
		msg: 'Lorem ipsum dolor sit amet hears farmer indemnity inherent.'
	});
}

function round_success_noti() {
	Lobibox.notify('success', {
		pauseDelayOnHover: true,
		size: 'mini',
		rounded: true,
		icon: 'bi bi-check2-circle',
		delayIndicator: false,
		continueDelayOnInactiveTab: false,
		position: 'top right',
		msg: 'Lorem ipsum dolor sit amet hears farmer indemnity inherent.'
	});
}
/* Notifications With Images*/
function img_default_noti() {
	Lobibox.notify('default', {
		pauseDelayOnHover: true,
		continueDelayOnInactiveTab: false,
		position: 'top right',
		img: 'assets/plugins/notifications/img/1.jpg', //path to image
		msg: 'Lorem ipsum dolor sit amet hears farmer indemnity inherent.'
	});
}

function img_info_noti() {
	Lobibox.notify('info', {
		pauseDelayOnHover: true,
		continueDelayOnInactiveTab: false,
		icon: 'bi bi-info-circle',
		position: 'top right',
		img: 'assets/plugins/notifications/img/2.jpg', //path to image
		msg: 'Lorem ipsum dolor sit amet hears farmer indemnity inherent.'
	});
}

function img_warning_noti() {
	Lobibox.notify('warning', {
		pauseDelayOnHover: true,
		icon: 'bi bi-exclamation-triangle',
		continueDelayOnInactiveTab: false,
		position: 'top right',
		img: 'assets/plugins/notifications/img/3.jpg', //path to image
		msg: 'Lorem ipsum dolor sit amet hears farmer indemnity inherent.'
	});
}

function img_error_noti() {
	Lobibox.notify('error', {
		pauseDelayOnHover: true,
		continueDelayOnInactiveTab: false,
		icon: 'bi bi-x-circle',
		position: 'top right',
		img: 'assets/plugins/notifications/img/4.jpg', //path to image
		msg: 'Lorem ipsum dolor sit amet hears farmer indemnity inherent.'
	});
}

function img_success_noti() {
	Lobibox.notify('success', {
		pauseDelayOnHover: true,
		continueDelayOnInactiveTab: false,
		position: 'top right',
		icon: 'bi bi-check2-circle',
		img: 'assets/plugins/notifications/img/5.jpg', //path to image
		msg: 'Lorem ipsum dolor sit amet hears farmer indemnity inherent.'
	});
}
/* Notifications With Images*/
function pos1_default_noti() {
	Lobibox.notify('default', {
		pauseDelayOnHover: true,
		continueDelayOnInactiveTab: false,
		position: 'center top',
		size: 'mini',
		msg: 'Lorem ipsum dolor sit amet hears farmer indemnity inherent.'
	});
}

function pos2_info_noti() {
	Lobibox.notify('info', {
		pauseDelayOnHover: true,
		icon: 'bi bi-info-circle',
		continueDelayOnInactiveTab: false,
		position: 'top left',
		size: 'mini',
		msg: 'Lorem ipsum dolor sit amet hears farmer indemnity inherent.'
	});
}

function pos3_warning_noti() {
	Lobibox.notify('warning', {
		pauseDelayOnHover: true,
		icon: 'bi bi-exclamation-triangle',
		continueDelayOnInactiveTab: false,
		position: 'top right',
		size: 'mini',
		msg: 'Lorem ipsum dolor sit amet hears farmer indemnity inherent.'
	});
}

function pos4_error_noti() {
	Lobibox.notify('error', {
		pauseDelayOnHover: true,
		icon: 'bi bi-x-circle',
		size: 'mini',
		continueDelayOnInactiveTab: false,
		position: 'bottom left',
		msg: 'Lorem ipsum dolor sit amet hears farmer indemnity inherent.'
	});
}

function pos5_success_noti() {
	Lobibox.notify('success', {
		pauseDelayOnHover: true,
		size: 'mini',
		icon: 'bi bi-check2-circle',
		continueDelayOnInactiveTab: false,
		position: 'bottom right',
		msg: 'Lorem ipsum dolor sit amet hears farmer indemnity inherent.'
	});
}
/* Animated Notifications*/
function anim1_noti() {
	Lobibox.notify('default', {
		pauseDelayOnHover: true,
		continueDelayOnInactiveTab: false,
		position: 'center top',
		showClass: 'fadeInDown',
		hideClass: 'fadeOutDown',
		width: 600,
		msg: 'Lorem ipsum dolor sit amet hears farmer indemnity inherent.'
	});
}

function anim2_noti() {
	Lobibox.notify('info', {
		pauseDelayOnHover: true,
		icon: 'bi bi-info-circle',
		continueDelayOnInactiveTab: false,
		position: 'center top',
		showClass: 'bounceIn',
		hideClass: 'bounceOut',
		width: 600,
		msg: 'Lorem ipsum dolor sit amet hears farmer indemnity inherent.'
	});
}

function anim3_noti() {
	Lobibox.notify('warning', {
		pauseDelayOnHover: true,
		continueDelayOnInactiveTab: false,
		icon: 'bi bi-exclamation-triangle',
		position: 'center top',
		showClass: 'zoomIn',
		hideClass: 'zoomOut',
		width: 600,
		msg: 'Lorem ipsum dolor sit amet hears farmer indemnity inherent.'
	});
}

function anim4_noti() {
	Lobibox.notify('error', {
		pauseDelayOnHover: true,
		continueDelayOnInactiveTab: false,
		icon: '',
		position: 'center top',
		showClass: 'lightSpeedIn',
		hideClass: 'lightSpeedOut',
		icon: 'bi bi-x-circle',
		width: 600,
		msg: 'Lorem ipsum dolor sit amet hears farmer indemnity inherent.'
	});
}

function anim5_noti() {
	Lobibox.notify('success', {
		pauseDelayOnHover: true,
		continueDelayOnInactiveTab: false,
		position: 'center top',
		showClass: 'rollIn',
		hideClass: 'rollOut',
		icon: 'bi bi-check2-circle',
		width: 600,
		msg: 'Lorem ipsum dolor sit amet hears farmer indemnity inherent.'
	});
}