<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Wallet;
use App\Models\WalletTransaction;
use App\Models\DriverProfile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Yajra\DataTables\DataTables;

class WalletTransactionController extends Controller
{
    //
    public function index(Request $request)
    {
        return view('admin.wallet.transaction.index');
    }



    public function getWalletDetails($id, Request $request)
    {
        try {
            $wallet = Wallet::findOrFail($id);
            // Get owner details
            if ($wallet->user_id) {
                $owner = User::find($wallet->user_id);
                $wallet->owner_name = $owner ? $owner->name : 'Unknown User';
                $wallet->owner_type = $owner && $owner->hasRole('driver') ? 'driver' : 'user';
            } else {
                $wallet->owner_name = 'Unknown';
                $wallet->owner_type = 'unknown';
            }
            // Get transactions
            $transactions = $wallet->transactions()
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get();
            // Calculate totals
            $totalRecharges = $wallet->transactions()
                ->where('transaction_type', 'recharge')
                ->sum('amount');
            $totalDeductions = $wallet->transactions()
                ->where('transaction_type', 'deduction')
                ->sum('amount');
            return response()->json([
                'success' => true,
                'data' => [
                    'wallet' => $wallet,
                    'transactions' => $transactions,
                    'total_recharges' => number_format($totalRecharges, 2),
                    'total_deductions' => number_format($totalDeductions, 2)
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load wallet details: ' . $e->getMessage()
            ]);
        }
    }
    public function getTransactions(Request $request)
    {
        try {
            $query = WalletTransaction::with(['wallet.user']);
            if ($request->has('search') && !empty($request->search)) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->whereHas('wallet.user', function ($q2) use ($search) {
                        $q2->where('name', 'like', "%{$search}%")
                            ->orWhere('email', 'like', "%{$search}%")
                            ->orWhere('id', 'like', "%{$search}%");
                    })
                        ->orWhere('wallet_id', 'like', "%{$search}%")
                        ->orWhere('id', 'like', "%{$search}%");
                });
            }
            if ($request->has('date') && !empty($request->date)) {
                $query->whereDate('created_at', $request->date);
            }
            $transactions = $query->orderBy('created_at', 'desc')->get();
            $data = $transactions->map(function ($transaction) {
                $wallet = $transaction->wallet;
                $ownerType = null;
                $ownerName = null;
                if ($wallet && $wallet->user) {
                    $owner = $wallet->user;
                    $ownerType = $owner->hasRole('driver') ? 'driver' : 'user';
                    $ownerName = $owner->name;
                }
                return [
                    'id' => $transaction->id,
                    'wallet_id' => $transaction->wallet_id,
                    'owner_type' => $ownerType,
                    'owner_name' => $ownerName,
                    'transaction_type' => $transaction->transaction_type,
                    'amount' => $transaction->amount,
                    'reference_type' => $transaction->reference_type,
                    'description' => $transaction->description,
                    'created_at' => $transaction->created_at,
                ];
            });
            return response()->json([
                'success' => true,
                'data' => $data
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load transactions: ' . $e->getMessage()
            ]);
        }
    }
    public function getTransactionDetails($id)
    {
        try {
            $transaction = WalletTransaction::findOrFail($id);
            $wallet = Wallet::findOrFail($transaction->wallet_id);
            $ownerType = null;
            if ($wallet->user) {
                $ownerType = $wallet->user->hasRole('driver') ? 'driver' : 'user';
            }
            $transaction->owner_type = $ownerType;
            return response()->json([
                'success' => true,
                'data' => $transaction
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load transaction details: ' . $e->getMessage()
            ]);
        }
    }
    public function addTransaction(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'wallet_id' => 'required|exists:wallets,id',
                'transaction_type' => 'required|in:recharge,deduction',
                'amount' => 'required|numeric|min:0.01',
                'reference_type' => 'required|in:recharge_code,ride_commission,refund,promotional',
                'description' => 'nullable|string|max:255'
            ]);
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }
            DB::beginTransaction();
            $wallet = Wallet::findOrFail($request->wallet_id);
            if ($request->transaction_type == 'deduction' && $wallet->balance < $request->amount) {
                return response()->json([
                    'success' => false,
                    'errors' => ['amount' => 'Insufficient wallet balance']
                ], 422);
            }
            if ($request->transaction_type == 'recharge') {
                $wallet->balance += $request->amount;
            } else {
                $wallet->balance -= $request->amount;
            }
            $wallet->save();
            $transaction = new WalletTransaction();
            $transaction->wallet_id = $request->wallet_id;
            $transaction->amount = $request->amount;
            $transaction->transaction_type = $request->transaction_type;
            $transaction->reference_type = $request->reference_type;
            $transaction->description = $request->description;
            $transaction->save();
            DB::commit();
            return response()->json([
                'success' => true,
                'message' => 'Transaction added successfully',
                'data' => [
                    'transaction' => $transaction,
                    'new_balance' => $wallet->balance
                ]
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to add transaction: ' . $e->getMessage()
            ]);
        }
    }
    public function exportTransactions()
    {
        $transactions = WalletTransaction::with(['wallet.user'])->get();
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="wallet_transactions.csv"',
        ];
        $callback = function () use ($transactions) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['ID', 'Wallet ID', 'Owner Type', 'Owner Name', 'Transaction Type', 'Amount', 'Reference', 'Description', 'Date']);
            foreach ($transactions as $transaction) {
                $wallet = $transaction->wallet;
                $ownerType = null;
                $ownerName = null;
                if ($wallet && $wallet->user) {
                    $owner = $wallet->user;
                    $ownerType = $owner->hasRole('driver') ? 'driver' : 'user';
                    $ownerName = $owner->name;
                }
                fputcsv($file, [
                    $transaction->id,
                    $transaction->wallet_id,
                    $ownerType,
                    $ownerName,
                    $transaction->transaction_type,
                    $transaction->amount,
                    $transaction->reference_type,
                    $transaction->description,
                    $transaction->created_at
                ]);
            }
            fclose($file);
        };
        return response()->stream($callback, 200, $headers);
    }
    public function getAllTransactionsData(Request $request)
    {
        $transactions = WalletTransaction::with(['wallet.user']);

        return DataTables::of($transactions)
            ->addColumn('owner_type', function ($transaction) {
                $wallet = $transaction->wallet;
                if ($wallet && $wallet->user) {
                    return $wallet->user->hasRole('driver') ? 'driver' : 'user';
                }
                return '-';
            })
            ->addColumn('owner_name', function ($transaction) {
                $wallet = $transaction->wallet;
                return $wallet && $wallet->user ? $wallet->user->name : '-';
            })
            ->make(true);
    }
}
