<?php

namespace App\Http\Controllers;


use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Wallet;
use App\Models\WalletTransaction;
use App\Models\DriverProfile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Barryvdh\DomPDF\Facade\Pdf;


class WalletController extends Controller
{
    public function index()
    {
        $wallets = Wallet::with(['user'])
        ->whereHas('user.roles', function ($query) {
            $query->where('name', 'user');
        })
        ->orderBy('id', 'desc')
        ->get();
        return view('admin.wallet.users', compact('wallets'));

    }
    public function getSummary()
    {
        try {
            $userWalletsCount   = Wallet::whereNotNull('user_id')->count();
            $driverWalletsCount = Wallet::whereNotNull('driver_id')->count();
            $totalUserBalance   = Wallet::whereNotNull('user_id')->sum('balance');
            $totalDriverBalance = Wallet::whereNotNull('driver_id')->sum('balance');

            return response()->json([
                'success' => true,
                'data' => [
                    'user_wallets_count' => $userWalletsCount,
                    'driver_wallets_count' => $driverWalletsCount,
                    'total_user_balance' => number_format($totalUserBalance, 2),
                    'total_driver_balance' => number_format($totalDriverBalance, 2)
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load wallet summary: ' . $e->getMessage()
            ]);
        }
    }
    public function createWallet(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'owner_type' => 'required|in:user,driver',
                'owner_id' => 'required|integer',
                'initial_balance' => 'numeric|min:0'
            ]);
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }
            if ($request->owner_type == 'user') {
                $owner = User::find($request->owner_id);
                if (!$owner) {
                    return response()->json([
                        'success' => false,
                        'errors' => ['owner_id' => 'User not found']
                    ], 422);
                }
                $existing = Wallet::where('user_id', $request->owner_id)->first();
                if ($existing) {
                    return response()->json([
                        'success' => false,
                        'errors' => ['owner_id' => 'Wallet already exists for this user']
                    ], 422);
                }
                $wallet = new Wallet();
                $wallet->user_id = $request->owner_id;
                $wallet->balance = $request->initial_balance ?? 0;
                $wallet->save();

            } else {
                $owner = DriverProfile::find($request->owner_id);
                if (!$owner) {
                    return response()->json([
                        'success' => false,
                        'errors' => ['owner_id' => 'Driver not found']
                    ], 422);
                }
                $existing = Wallet::where('driver_id', $request->owner_id)->first();
                if ($existing) {
                    return response()->json([
                        'success' => false,
                        'errors' => ['owner_id' => 'Wallet already exists for this driver']
                    ], 422);
                }
                $wallet = new Wallet();
                $wallet->driver_id = $request->owner_id;
                $wallet->balance = $request->initial_balance ?? 0;
                $wallet->save();
            }
            if ($request->initial_balance > 0) {
                $transaction = new WalletTransaction();
                $transaction->wallet_id = $wallet->id;
                $transaction->amount = $request->initial_balance;
                $transaction->transaction_type = 'recharge';
                $transaction->reference_type = 'recharge_code';
                $transaction->description = 'Initial wallet balance';
                $transaction->save();
            }
            return response()->json([
                'success' => true,
                'message' => 'Wallet created successfully',
                'data' => $wallet
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create wallet: ' . $e->getMessage()
            ]);
        }
    }
    public function exportUserWallets()
    {
        $wallets = Wallet::with('user')
            ->whereNotNull('user_id')
            ->get();

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="user_wallets.csv"',
        ];

        $callback = function() use ($wallets) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['ID', 'User ID', 'Name', 'Email', 'Phone', 'Balance', 'Created At']);
            foreach ($wallets as $wallet) {
                $user = $wallet->user;
                fputcsv($file, [
                    $wallet->id,
                    $wallet->user_id,
                    $user ? $user->name : null,
                    $user ? $user->email : null,
                    $user ? $user->phone : null,
                    $wallet->balance,
                    $wallet->created_at
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
    public function getUserWallets(Request $request)
    {
        try {
            $query = Wallet::with('user')
                ->whereNotNull('user_id');
            if ($request->has('search') && !empty($request->search)) {
                $search = $request->search;
                $query->whereHas('user', function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%")
                      ->orWhere('id', 'like', "%{$search}%");
                });
                $query->orWhere('id', 'like', "%{$search}%");
            }

            if ($request->has('balance') && !empty($request->balance)) {
                switch ($request->balance) {
                    case 'low':
                        $query->where('balance', '<', 10);
                        break;
                    case 'medium':
                        $query->whereBetween('balance', [10, 100]);
                        break;
                    case 'high':
                        $query->where('balance', '>', 100);
                        break;
                }
            }
            if ($request->has('date') && !empty($request->date)) {
                $query->whereDate('created_at', $request->date);
            }
            $wallets = $query->orderBy('id', 'desc')->get();
            $data = $wallets->map(function($wallet) {
                $user = $wallet->user;
                return [
                    'id' => $wallet->id,
                    'user_id' => $wallet->user_id,
                    'user_name' => $user ? $user->name : null,
                    'email' => $user ? $user->email : null,
                    'phone' => $user ? $user->phone : null,
                    'balance' => $wallet->balance,
                    'created_at' => $wallet->created_at,
                    'last_transaction' => $wallet->transactions()->max('created_at'),
                ];
            });
            return response()->json([
                'success' => true,
                'data' => $data
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load user wallets: ' . $e->getMessage()
            ]);
        }
    }
    public function getUserWalletsData(Request $request)
    {
        $wallets = Wallet::with(['user'])
            ->whereHas('user.roles', function ($q) {
                $q->where('name', 'user');
            });

        return \Yajra\DataTables\DataTables::of($wallets)
            ->addColumn('user_name', function ($wallet) {
                return $wallet->user ? $wallet->user->name : null;
            })
            ->addColumn('email', function ($wallet) {
                return $wallet->user ? $wallet->user->email : null;
            })
            ->addColumn('phone', function ($wallet) {
                return $wallet->user ? $wallet->user->phone : null;
            })
            ->addColumn('last_transaction', function ($wallet) {
                return $wallet->transactions()->max('created_at');
            })
            ->addColumn('action', function ($wallet) {
                $url = route('admin.profile.wallets.show', ['user' => $wallet->user_id]);
                return '<a href="' . $url . '" class="btn btn-sm btn-primary">show</a>';
            })
            ->rawColumns(['last_transaction', 'action'])
             ->make(true);
    }
    public function exportUserWalletsPdf()
    {
        $wallets = Wallet::with(['user'])
            ->whereHas('user.roles', function ($query) {
                $query->where('name', 'user');
            })
            ->orderBy('id', 'desc')
            ->get();

        $pdf = Pdf::loadView('admin.wallet.user_pdf', compact('wallets'));
        return $pdf->download('users_wallets.pdf');
    }
    public function indexAdmin()
    {
        $wallets = Wallet::with('user')
        ->whereHas('user.roles', function ($query) {
            $query->where('name', 'admin');
        })
        ->orderBy('id', 'desc')
        ->get();
        return view('admin.wallet.admin', compact('wallets'));

    }
    public function exportAdminWallets()
    {
        $wallets = Wallet::with('user')
            ->whereHas('user.roles', function ($q) {
                $q->where('name', 'admin');
            })
            ->orderBy('id', 'desc')
            ->get();

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="admin_wallets.csv"',
        ];

        $callback = function() use ($wallets) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['ID', 'User ID', 'Name', 'Email', 'Phone', 'Balance', 'Created At']);
            foreach ($wallets as $wallet) {
                $user = $wallet->user;
                fputcsv($file, [
                    $wallet->id,
                    $wallet->user_id,
                    $user ? $user->name : null,
                    $user ? $user->email : null,
                    $user ? $user->phone : null,
                    $wallet->balance,
                    $wallet->created_at
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
    public function getAdminWalletsData(Request $request)
    {
        $wallets = Wallet::with(['user'])
            ->whereHas('user.roles', function ($q) {
                $q->where('name', 'admin');
            });

        return \Yajra\DataTables\DataTables::of($wallets)
            ->addColumn('user_name', function ($wallet) {
                return $wallet->user ? $wallet->user->name : null;
            })
            ->addColumn('email', function ($wallet) {
                return $wallet->user ? $wallet->user->email : null;
            })
            ->addColumn('phone', function ($wallet) {
                return $wallet->user ? $wallet->user->phone : null;
            })
            ->addColumn('last_transaction', function ($wallet) {
                return $wallet->transactions()->max('created_at');
            })
            // ->addColumn('action', function ($wallet) {
            //     $url = route('admin.profile.wallets.show', ['user' => $wallet->user_id]);
            //     return '<a href="' . $url . '" class="btn btn-sm btn-primary">show</a>';
            // })
            // ->rawColumns(['last_transaction', 'action'])

            ->rawColumns(['last_transaction'])
            ->make(true);
    }
    public function exportAdminWalletsPdf()
    {
        $wallets = Wallet::with(['user'])
            ->whereHas('user.roles', function ($query) {
                $query->where('name', 'admin');
            })
            ->orderBy('id', 'desc')
            ->get();

        $pdf = Pdf::loadView('admin.wallet.admin_pdf', compact('wallets'));
        return $pdf->download('admins_wallets.pdf');
    }

}
