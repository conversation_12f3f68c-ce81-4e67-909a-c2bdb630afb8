<?php


namespace App\Http\Controllers\Api\Shared;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;
use App\Http\Resources\Shared\TripHistoryResource;


class TripHistoryController extends Controller
{
    //
    public function index()
    {
        $user = Auth::user();

        $history = $user->tripHistory()->with(['trip', 'user'])->get();

        return TripHistoryResource::collection($history);
    }

}
