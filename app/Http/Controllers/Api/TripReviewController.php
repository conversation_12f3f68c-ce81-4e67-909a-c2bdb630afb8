<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Requests\Api\TripReviewRequest;
use App\Models\TripReview;
use App\Models\Trip;
use Illuminate\Support\Facades\Auth;
use App\Http\Resources\Shared\TripsReviewsResource;

class TripReviewController extends Controller
{
    public function show(Request $request, $tripId)
    {
        $trip = Trip::findOrFail($tripId);

        $reviews = Auth::user()
            ->tripReviews()
            ->where('trip_id', $trip->id)
            ->with(['user', 'trip'])
            ->get();

        return TripsReviewsResource::collection($reviews);
    }

    public function store(TripReviewRequest $request, $tripId)
    {
        $trip = Trip::findOrFail($tripId);

        $user = Auth::user();
        $data = $request->validated();
        $review = TripReview::create([
            'trip_id' => $trip->id,
            'user_id' => $user->id,
            'is_driver'=> $user->hasRole('user') ? 0:1 ,
            'rating' => $data['rating'],
        ]);
        return response()->json([
            'message' => 'Thank you for your feedback!',
            'data' => $review
        ], 201);
    }
}
