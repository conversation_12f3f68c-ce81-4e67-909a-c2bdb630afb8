<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Http\Requests\Api\Profile\User\UserUpdateProfileRequest;
use App\Models\User;
use Exception;
class UserProfileController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {

    }

    public function show(User $user)
    {

    }
    public function update(UserUpdateProfileRequest $request)
    {
        $user = Auth::user();
        $user->update($request->validated());
        return response()->json([
            "message" => "update is Success",
            "user" => $user
        ]);
    }
    public function destroy()
    {
        try {
            $user = User::find(Auth::id());

            if (!$user) {
                return response()->json([
                    'message' => 'User not found'
                ], 404);
            }

            $user->forceDelete();

            return response()->json([
                'message' => 'Account deleted successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'message' => 'Something went wrong',
                'error' => $e->getMessage()
            ], 500);
        }
    }


}
