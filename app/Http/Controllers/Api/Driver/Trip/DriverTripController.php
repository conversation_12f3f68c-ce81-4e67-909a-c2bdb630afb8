<?php
namespace App\Http\Controllers\Api\Driver\Trip;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Trip;
use Illuminate\Support\Facades\Validator;
use App\Services\Firebase\FirebaseService;
use App\Models\Vehicle\VehicleType;
use Carbon\Carbon;
use App\Services\Location\DistanceService;
use App\Models\DriverAvailability;


class DriverTripController extends Controller
{
    protected $firebase;
    protected $distanceService;

    public function __construct(FirebaseService $firebaseService, DistanceService $distanceService)
    {
        $this->distanceService = $distanceService;
        $this->firebase = $firebaseService;
    }


    public function acceptTrip(Request $request, $tripId)
    {
        $validator = Validator::make($request->all(), []);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $trip = Trip::findOrFail($tripId);

            if (!auth()->user()->hasRole('driver')) {
                return response()->json(['message' => 'Unauthorized, not a driver.'], 403);
            }

            if ($trip->status !== 'pending') {
                return response()->json(['message' => 'Trip is no longer available to accept.'], 400);
            }

            $trip->status = 'accepted';
            $trip->driver_id = auth()->user()->id;
            $trip->save();


            $driverAvailability = DriverAvailability::where('driver_id', auth()->user()->id)->first();
            if ($driverAvailability) {
                $driverAvailability->is_available = false;
                $driverAvailability->save();
            }

            $this->firebase->storeTripInFirebase($trip->id, 'accepted');

            return response()->json([
                'message' => 'Trip accepted successfully.',
                'trip' => $trip
            ]);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Error accepting the trip: ' . $e->getMessage()], 500);
        }
    }


    public function startTrip(Request $request, $tripId)
    {
        $validator = Validator::make($request->all(), [
            'pickup_lat' => 'required|numeric',
            'pickup_lng' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $trip = Trip::findOrFail($tripId);

            if (!auth()->user()->hasRole('driver')) {
                return response()->json(['message' => 'Unauthorized, not a driver.'], 403);
            }

            if ($trip->status !== 'accepted') {
                return response()->json(['message' => 'Trip cannot be started.'], 400);
            }

            $trip->status = 'in_progress';
            $trip->driver_accept_lat = $request->pickup_lat;
            $trip->driver_accept_lng = $request->pickup_lng;
            $trip->save();

            $driverAvailability = DriverAvailability::where('driver_id', auth()->user()->id)->first();
            if ($driverAvailability) {
                $driverAvailability->is_available = false;
                $driverAvailability->save();
            }
            $this->firebase->storeTripInFirebase($trip->id, 'in_progress');

            return response()->json([
                'message' => 'Trip started successfully.',
                'trip' => $trip
            ]);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Error starting the trip: ' . $e->getMessage()], 500);
        }
    }

    public function cancelTrip(Request $request, $tripId)
    {
        try {
            $trip = Trip::findOrFail($tripId);

            if (!auth()->user()->hasRole('driver')) {
                return response()->json(['message' => 'Unauthorized, not a driver.'], 403);
            }

            if ($trip->status !== 'accepted') {
                return response()->json(['message' => 'Trip cannot be canceled at this stage.'], 400);
            }

            $trip->status = 'cancelled';
            $trip->cancelled_by = 'driver';
            $trip->cancelled_at = now();
            $trip->save();


            $driverAvailability = DriverAvailability::where('driver_id', auth()->user()->id)->first();
            if ($driverAvailability) {
                $driverAvailability->is_available = true;
                $driverAvailability->save();
            }

            $this->firebase->storeTripInFirebase($trip->id, 'cancelled');

            return response()->json([
                'message' => 'Trip cancelled successfully.',
                'trip' => $trip
            ]);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Error canceling the trip: ' . $e->getMessage()], 500);
        }
    }


public function endTrip(Request $request, $tripId)
{
    $validator = Validator::make($request->all(), [
        'final_fare' => 'required|numeric',  // The final fare passed in the request
    ]);

    if ($validator->fails()) {
        return response()->json([
            'message' => 'Validation failed',
            'errors' => $validator->errors()
        ], 422);
    }

    try {
        // Find the trip by its ID
        $trip = Trip::findOrFail($tripId);

        // Ensure that the authenticated user is a driver
        if (!auth()->user()->hasRole('driver')) {
            return response()->json(['message' => 'Unauthorized, not a driver.'], 403);
        }

        // Ensure the trip is in the "in_progress" status
        if ($trip->status !== 'in_progress') {
            return response()->json(['message' => 'Trip cannot be ended at this stage.'], 400);
        }

        // Recalculate the final fare based on the actual distance and time
        $vehicleType = VehicleType::findOrFail($trip->vehicle_type_id);
        $startLat = $trip->pickup_lat;
        $startLong = $trip->pickup_lng;
        $endLat = $trip->dropoff_lat;
        $endLong = $trip->dropoff_lng;

        // Calculate the actual distance (you may need a more sophisticated method here)
        $distance = $this->distanceService->haversine($startLat, $startLong, $endLat, $endLong);

        // Assuming you track the trip's actual time spent or ETA, you can use this as well
        $currentHour = Carbon::now()->hour;
        $isNightTime = ($currentHour >= 18 || $currentHour < 6);

        // Apply the rates based on the time of day (night or day rates)
        $perKmRate = $isNightTime ? $vehicleType->night_per_km_rate : $vehicleType->day_per_km_rate;
        $perMinuteRate = $isNightTime ? $vehicleType->night_per_minute_rate : $vehicleType->day_per_minute_rate;

        // If you track the ETA or trip duration, use it here (this is an example, modify as needed)
        $tripDuration = 10; // You could calculate this based on start and end time, or ETA

        // Calculate the fare using distance, vehicle type rate, and time
        $totalBeforeDiscount = $vehicleType->start_fare + ($distance * $perKmRate) + ($tripDuration * $perMinuteRate);

        // Apply any promo code discount
        $promoCodeDiscount = 0;
        if ($trip->promo_code && $trip->promo_code === "DISCOUNT10") {
            $promoCodeDiscount = 10;
        }

        // Final fare calculation
        $finalFare = $totalBeforeDiscount - ($totalBeforeDiscount * $promoCodeDiscount) / 100;

        // Update the trip with the final fare and status
        $trip->final_fare = $finalFare;
        $trip->status = 'completed';
        $trip->completed_at = now();  // Set the time the trip was completed
        $trip->save();

        $driverAvailability = DriverAvailability::where('driver_id', auth()->user()->id)->first();
        if ($driverAvailability) {
            $driverAvailability->is_available = true;
            $driverAvailability->save();
        }

        // Store the trip as completed in Firebase
        $this->firebase->storeTripInFirebase($trip->id, 'completed');

        return response()->json([
            'message' => 'Trip ended successfully.',
            'trip' => $trip,
        ]);
    } catch (\Exception $e) {
        return response()->json(['message' => 'Error ending the trip: ' . $e->getMessage()], 500);
    }
}


    public function getDriverTrip($tripId)
    {
        try {
            $trip = Trip::where('driver_id', auth()->id()) // Ensure the driver is the one assigned to the trip
                        ->findOrFail($tripId);

            return response()->json([
                'message' => 'Trip fetched successfully.',
                'trip' => $trip
            ]);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Error fetching the trip: ' . $e->getMessage()], 500);
        }
    }
}
