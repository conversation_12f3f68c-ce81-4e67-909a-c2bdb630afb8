<?php

namespace App\Http\Controllers\Api\Driver;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use App\Http\Controllers\Api\Base\AuthBaseController;
use App\Services\Interfaces\IAuthService;
use App\Http\Requests\Api\Auth\DriverRegisterRequest;
use App\Http\Requests\Api\Auth\DriverLoginRequest;
use Illuminate\Support\Facades\Auth;
use Exception;

class DriverAuthController extends AuthBaseController
{
    protected $authService;

    public function __construct(IAuthService $authService)
    {
        $this->authService = $authService;
    }

    public function register(DriverRegisterRequest $request)
    {
        try {

            // Validate the incoming request
            $data = $request->validated();
            $data['role'] = 'driver';

             // Ensure nested arrays exist
            $data['driver_profile'] = $data['driver_profile'] ?? [];
            $data['vehicle'] = $data['vehicle'] ?? [];

            // Handle the file uploads for the avatar
            if ($request->hasFile('avatar')) {
                $data['avatar'] = $request->file('avatar')->store('avatars', 'public');
            }

            // Handle driver profile documents
            if ($request->hasFile('driver_profile.id_card_front')) {
                $data['driver_profile']['id_card_front'] = $request->file('driver_profile.id_card_front')->store('driver_profiles/id_card', 'public');
            }
            if ($request->hasFile('driver_profile.id_card_back')) {
                $data['driver_profile']['id_card_back'] = $request->file('driver_profile.id_card_back')->store('driver_profiles/id_card', 'public');
            }
            if ($request->hasFile('driver_profile.license_front')) {
                $data['driver_profile']['license_front'] = $request->file('driver_profile.license_front')->store('driver_profiles/license', 'public');
            }
            if ($request->hasFile('driver_profile.license_back')) {
                $data['driver_profile']['license_back'] = $request->file('driver_profile.license_back')->store('driver_profiles/license', 'public');
            }
            if ($request->hasFile('driver_profile.vehicle_license_front')) {
                $data['driver_profile']['vehicle_license_front'] = $request->file('driver_profile.vehicle_license_front')->store('driver_profiles/vehicle_license', 'public');
            }
            if ($request->hasFile('driver_profile.vehicle_license_back')) {
                $data['driver_profile']['vehicle_license_back'] = $request->file('driver_profile.vehicle_license_back')->store('driver_profiles/vehicle_license', 'public');
            }
            // car img
            if ($request->hasFile('driver_profile.interior_front_seats')) {
                $data['driver_profile']['interior_front_seats'] = $request->file('driver_profile.interior_front_seats')->store('driver_profiles/in_vehicle', 'public');
            }
            if ($request->hasFile('driver_profile.interior_back_seats')) {
                $data['driver_profile']['interior_back_seats'] = $request->file('driver_profile.interior_back_seats')->store('driver_profiles/in_vehicle', 'public');
            }

            if ($request->hasFile('driver_profile.exterior_front_side')) {
                $data['driver_profile']['exterior_front_side'] = $request->file('driver_profile.exterior_front_side')->store('driver_profiles/out_vehicle', 'public');
            }
            if ($request->hasFile('driver_profile.exterior_back_side')) {
                $data['driver_profile']['exterior_back_side'] = $request->file('driver_profile.exterior_back_side')->store('driver_profiles/out_vehicle', 'public');
            }

            // ✅ Handle vehicle image upload
            if ($request->hasFile('vehicle.image_url')) {
                $data['vehicle']['image_url'] = $request->file('vehicle.image_url')->store('vehicles/images', 'public');
            }


            // Register the user
            $user = $this->authService->register($data);

            // Respond with success message
            return response()->json([
                'message' => 'Registered successfully. OTP sent to phone.',
            ], 201);
        } catch (ValidationException $e) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            Log::error('Register Error: '.$e->getMessage());
            return response()->json([
                'message' => 'Something went wrong',
                'error' => $e->getMessage(),
            ], 500);
        }
    }


    public function login(DriverLoginRequest $request)
    {
        try {
            $data = $request->validated();

            $user = $this->authService->login($data['phone'], $data['password'], 'driver');



            if ((!$user->driverProfile || !$user->driverProfile->is_driver_verified)) {
                return $this->jsonError('Driver is not verified wait for admin approval', 401); // Unauthorized
            }

            $token = $user->createToken('api-token')->plainTextToken;

            return $this->jsonSuccess([
                'message' => 'Login successful.',
                'user' => $user,
                'token' => $token
            ]);
        } catch (ValidationException $e) {
            return $this->jsonError('Validation failed', 422, $e->errors());
        } catch (\Exception $e) {
            Log::error('Login Error: '.$e->getMessage());
            return $this->jsonError('Something went wrong', 500, ['error' => $e->getMessage()]);
        }
    }


}
