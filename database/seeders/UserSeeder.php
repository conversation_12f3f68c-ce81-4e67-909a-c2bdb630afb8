<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Wallet;

use Spatie\Permission\Models\Role;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Make sure roles exist
        $roles = ['admin', 'driver', 'user'];
        $users = [];
        foreach ($roles as $role) {
            Role::firstOrCreate(['name' => $role]);
        }
        // Create Admin if not exists
           if (!User::where('email', '<EMAIL>')->exists()) {
            $users[]= User::create([
                'name' => 'dynamite',
                'email' => '<EMAIL>',
                "phone"=> '12345',
                'password' => bcrypt('password'),
            ])->assignRole('admin');
        }
            if (!User::where('email', '<EMAIL>')->exists()) {
            $users[]= User::create([
                'name' => 'user',
                'email' => '<EMAIL>',
                "phone"=> '123456',
                'password' => bcrypt('password'),
            ])->assignRole('admin');
        }
        if (!User::where('email', '<EMAIL>')->exists()) {
            $users[]= User::create([
                'name' => 'Admin',
                'email' => '<EMAIL>',
                "phone"=> '1234567',

                'password' => bcrypt('password'),
            ])->assignRole('admin');
        }
        // Create Driver if not exists
        if (!User::where('email', '<EMAIL>')->exists()) {
            $users[]= User::create([
                'name' => 'Driver User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                "phone"=> '12345678',
            ])->assignRole('driver');
        }
        // Create Regular User if not exists
        if (!User::where('email', '<EMAIL>')->exists()) {
            $users[]= User::create([
                'name' => 'Regular User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                "phone"=> '123456789',
            ])->assignRole('user');
        }
      
    }
}
