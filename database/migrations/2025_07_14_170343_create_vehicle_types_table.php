<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vehicle_types', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique(); // e.g., Taxi, Economy, Luxury
            $table->text('description')->nullable();
            $table->decimal('start_fare', 8, 2); // The starting fare (e.g., 0.5)
            $table->decimal('day_per_km_rate', 8, 2); // Regular rate per km for day
            $table->decimal('night_per_km_rate', 8, 2); // Regular rate per km for night
            $table->decimal('day_per_minute_rate', 8, 2); // Per minute rate for day trips
            $table->decimal('night_per_minute_rate', 8, 2); // Per minute rate for night trips
            $table->boolean('is_active')->default(true);
            $table->string('icon_url')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vehicle_types');
    }
};
