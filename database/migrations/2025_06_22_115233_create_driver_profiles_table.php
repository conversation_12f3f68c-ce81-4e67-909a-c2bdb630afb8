<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('driver_profiles', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('user_id')->unique();
            
            // Driver profile fields

            $table->string('id_card_front')->nullable();  // Front side of ID image
            $table->string('id_card_back')->nullable();   // Back side of ID image
            $table->string('license_front')->nullable();  // Front side of Driver's License image
            $table->string('license_back')->nullable();   // Back side of Driver's License image
            $table->string('vehicle_license_front')->nullable();  // Front side of Vehicle License image
            $table->string('vehicle_license_back')->nullable();   // Back side of Vehicle License image

            $table->timestamps();

            // Foreign key constraint
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('driver_profiles');
    }
};
