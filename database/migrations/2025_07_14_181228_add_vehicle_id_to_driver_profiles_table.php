<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('driver_profiles', function (Blueprint $table) {
            $table->unsignedBigInteger('vehicle_id')->nullable()->after('user_id');  // Add the vehicle_id column
                $table->foreign('vehicle_id')  // Set the foreign key constraint
                    ->references('id')
                    ->on('vehicles')
                    ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('driver_profiles', function (Blueprint $table) {
            $table->dropForeign(['vehicle_id']);  // Drop foreign key constraint
            $table->dropColumn('vehicle_id');
        });
    }
};
