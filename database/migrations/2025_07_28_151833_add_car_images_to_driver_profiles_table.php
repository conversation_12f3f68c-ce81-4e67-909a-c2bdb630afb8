<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCarImagesToDriverProfilesTable extends Migration
{
    public function up()
    {

        Schema::table('driver_profiles', function (Blueprint $table) {
            $table->string('interior_front_seats')->nullable()->after('vehicle_license_back');
            $table->string('interior_back_seats')->nullable()->after('interior_front_seats');
            $table->string('exterior_front_side')->nullable()->after('interior_back_seats');
            $table->string('exterior_back_side')->nullable()->after('exterior_front_side');
        });
    }

    public function down()
    {
        Schema::table('driver_profiles', function (Blueprint $table) {
            $table->dropColumn([
                'interior_front_seats',  // Correct column name
                'interior_back_seats',   // Correct column name
                'exterior_front_side',   // Correct column name
                'exterior_back_side',    // Correct column name
            ]);
        });
    }
}
