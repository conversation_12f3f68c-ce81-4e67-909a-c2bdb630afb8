<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\ContactUsController;
use App\Http\Controllers\Api\InviteController;
use App\Http\Controllers\Api\TripReviewController;
use App\Http\Controllers\Notification\FcmTokenController;
use App\Http\Controllers\Notification\NotificationController;
use App\Http\Controllers\Api\Shared\TripHistoryController;
use App\Http\Controllers\Api\User\UserLocationController;
use App\Http\Controllers\Api\User\CouponsController;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');
Route::middleware('auth:sanctum')->group(function () {

    Route::get('/get-all-notification', [NotificationController::class, 'getAllNotification']);
    Route::put('/update-device-token', [FcmTokenController::class, "store"]);
    Route::post('/tokens/create', function (Request $request) {
        $token = $request->user()->createToken($request->token_name);
        return response()->json([
            'token' => $token->plainTextToken
        ]);
    });
});

require __DIR__ . '/apiAuth.php';
Route::post('/broadcast-notification', [NotificationController::class, 'sendToAll']);
Route::post('/send-to-user-notification', [NotificationController::class, 'sendToUser']);
Route::prefix('shared')->middleware('auth:sanctum')->group(function () {
    Route::get('/trip-history', [TripHistoryController::class, 'index']);
    Route::get('/invite-code', [InviteController::class, 'getInviteCode']);
    Route::post('/contact-us', [ContactUsController::class, 'store']);
    Route::get('/trips/{trip}/reviews', [TripReviewController::class, 'show']);
    Route::post('/trips/{trip}/reviews', [TripReviewController::class, 'store']);
});

Route::post('/broadcast-notification', [NotificationController::class, 'sendToAll']);
Route::post('/send-to-user-notification', [NotificationController::class, 'sendToUser']);


Route::prefix('user')->middleware('auth:sanctum')->group(function () {
    Route::post('/coupons/apply', [CouponsController::class, 'applyCoupon']);
    Route::post('/coupons/record-usage', [CouponsController::class, 'recordUsage']);
});

// Support Tickets API
Route::middleware('auth:sanctum')->group(function () {
    Route::prefix('tickets')->group(function () {
        Route::get('/', [App\Http\Controllers\Api\TicketController::class, 'index']);
        Route::post('/', [App\Http\Controllers\Api\TicketController::class, 'store']);
        Route::get('/categories', [App\Http\Controllers\Api\TicketController::class, 'categories']);
        Route::get('/stats', [App\Http\Controllers\Api\TicketController::class, 'stats']);
        Route::get('/{id}', [App\Http\Controllers\Api\TicketController::class, 'show']);
        Route::post('/{id}/reply', [App\Http\Controllers\Api\TicketController::class, 'reply']);
    });
});

