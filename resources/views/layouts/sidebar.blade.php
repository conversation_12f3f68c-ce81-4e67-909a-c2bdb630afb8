<aside class="sidebar-wrapper" data-simplebar="true">
    <div class="sidebar-header">
        <div class="logo-icon">
            <img src="{{ asset('assets/images/logo-icon.png') }}" class="logo-img" alt="">
        </div>
        <div class="logo-name flex-grow-1">
            <h5 class="mb-0">Maxton</h5>
        </div>
        <div class="sidebar-close">
            <span class="material-icons-outlined">close</span>
        </div>
    </div>
    <div class="sidebar-nav">
        <ul class="metismenu" id="sidenav">
            @if ('permission:view_dashboard')
                <li>
                    <a href="javascript:;" class="has-arrow">
                        <div class="parent-icon"><i class="material-icons-outlined">home</i>
                        </div>
                        <div class="menu-title">Dashboard</div>
                    </a>
                    <ul>
                        <li><a href="{{ route('dashboard') }}"><i
                                    class="material-icons-outlined">arrow_right</i>Analysis</a>
                        </li>
                    </ul>
                </li>
            @endif
            @if ('permission:view_users')
                <li>
                    <a href="javascript:;" class="has-arrow">
                        <div class="parent-icon"><i class="material-icons-outlined">person</i>
                        </div>
                        <div class="menu-title">Users</div>
                    </a>
                    <ul>
                        <li><a href="{{ route('admin.users.index') }}"><i
                                    class="material-icons-outlined">arrow_right</i>Users</a>
                        </li>
                    </ul>
                </li>
            @endif
            @if ('permission:view_drivers')
                <li>
                    <a href="javascript:;" class="has-arrow">
                        <div class="parent-icon"><i class="material-icons-outlined">person</i>
                        </div>
                        <div class="menu-title">Drivers</div>
                    </a>
                    <ul>
                        <li><a href="{{ route('admin.drivers.index') }}"><i
                                    class="material-icons-outlined">arrow_right</i>Drivers</a>
                        </li>
                        <li><a href="{{ route('admin.drivers.unverified.index') }}"><i
                                    class="material-icons-outlined">arrow_right</i>Unverified Drivers</a>
                        </li>

                        <li><a href="{{ route('admin.drivers.available.index') }}"><i
                                    class="material-icons-outlined">arrow_right</i>Available Drivers</a>
                        </li>

                        <li><a href="{{ route('admin.drivers.unavailable.index') }}"><i
                                    class="material-icons-outlined">arrow_right</i>Unavailable Drivers</a>
                        </li>
                    </ul>
                </li>
            @endif

            @if ('permission:view_countries')
                <li>
                    <a href="javascript:;" class="has-arrow">
                        <div class="parent-icon"><i class="material-icons-outlined">place</i></div>
                        <div class="menu-title">Locations</div>
                    </a>
                    <ul>
                        <li><a href="{{ route('admin.countries.index') }}"><i
                                    class="material-icons-outlined">arrow_right</i>Countries</a>
                        </li>
                        <li><a href="{{ route('admin.cities.index') }}"><i
                                    class="material-icons-outlined">arrow_right</i>Cities</a>
                        </li>
                        <li><a href="{{ route('admin.districts.index') }}"><i
                                    class="material-icons-outlined">arrow_right</i>Areas</a>
                    </ul>
                </li>
            @endif
            @if ('permission:view_vehicle_types')
                <li>
                    <a href="javascript:;" class="has-arrow">
                        <div class="parent-icon"><i class="material-icons-outlined">directions_car</i></div>
                        <div class="menu-title">Vehicles</div>
                    </a>
                    <ul>
                        <li><a href="{{ route('admin.vehicle_types.index') }}"><i
                                    class="material-icons-outlined">category</i>Vehicle Types</a></li>
                    </ul>
                </li>
            @endif

            @if ('permission:view_contacts')
                <li>
                    <a href="javascript:;" class="has-arrow">
                        <div class="parent-icon"><i class="material-icons-outlined">contacts</i>
                        </div>
                        <div class="menu-title">Contacts</div>
                    </a>
                    <ul>
                        <li><a href="{{ route('admin.contacts.all.page') }}"><i
                                    class="material-icons-outlined">person</i>All</a>
                        </li>
                        <li><a href="{{ route('admin.contacts.drivers.page') }}"><i
                                    class="material-icons-outlined">person</i>Drivers</a>
                        </li>
                        <li><a href="{{ route('admin.contacts.users.page') }}"><i
                                    class="material-icons-outlined">person</i>Users</a>
                        </li>

                    </ul>
                </li>
            @endif

            {{-- Support Tickets Section --}}
            @if('permission:view_tickets')
                <li>
                    <a href="javascript:;" class="has-arrow">
                        <div class="parent-icon"><i class="material-icons-outlined">support_agent</i></div>
                        <div class="menu-title">Support Tickets</div>
                    </a>
                    <ul>
                        @if ('permission:view_tickets')
                            <li><a href="{{ route('admin.tickets.index') }}"><i
                                        class="material-icons-outlined">arrow_right</i>All Tickets</a>
                            </li>
                        @endif
                        @if ('permission:view_ticket_categories')
                            <li><a href="{{ route('admin.ticket_categories.index') }}"><i
                                        class="material-icons-outlined">arrow_right</i>Categories</a>
                            </li>
                        @endif
                    </ul>
                </li>
            @endif

            {{-- Trips --}}
            @if ('permission:view_trips')
                <li>
                    <a href="javascript:;" class="has-arrow">
                        <div class="parent-icon"><i class="material-icons-outlined">local_taxi</i></div>
                        <div class="menu-title">Trips</div>
                    </a>
                    <ul>
                        <li><a href="{{ route('admin.trips.index', ['status' => 'pending']) }}"><i
                                    class="material-icons-outlined">arrow_right</i>Pending</a></li>
                        <li><a href="{{ route('admin.trips.index', ['status' => 'accepted']) }}"><i
                                    class="material-icons-outlined">arrow_right</i>Accepted</a></li>
                        <li><a href="{{ route('admin.trips.index', ['status' => 'in_progress']) }}"><i
                                    class="material-icons-outlined">arrow_right</i>In Progress</a></li>
                        <li><a href="{{ route('admin.trips.index', ['status' => 'completed']) }}"><i
                                    class="material-icons-outlined">arrow_right</i>Completed</a></li>
                        <li><a href="{{ route('admin.trips.index', ['status' => 'cancelled']) }}"><i
                                    class="material-icons-outlined">arrow_right</i>Cancelled</a></li>
                    </ul>
                </li>
            @endif
            @if ('permission:view_currencies')
                <li>
                    <a href="{{ route('admin.currencies.index') }}">
                        <div class="parent-icon"><i class="material-icons-outlined">attach_money</i></div>
                        <div class="menu-title">currencies</div>
                    </a>
                </li>
            @endif
            @if ('permission:manage_country_currencies')
                <li>
                    <a href="{{ route('admin.countrycurrencies.index') }}">
                        <div class="parent-icon"><i class="material-icons-outlined">public</i></div>
                        <div class="menu-title">Country Currencies</div>
                    </a>
                </li>
            @endif

            @can('permission:view_coupons')
                <li>
                    <a href="{{ route('admin.coupons.index') }}">
                        <div class="parent-icon">
                            <i class="material-icons-outlined">local_offer</i>
                        </div>
                        <div class="menu-title">Coupons</div>
                    </a>
                </li>
            @endcan

            @if ('permission:view_notifications')
                <li class="sidebar-item">
                    <a href="{{ route('admin.notfiction.index') }}" class="sidebar-link">
                        <div class="parent-icon"><i class="material-icons-outlined">notifications_active</i></div>
                        <div class="menu-title">Notifications</div>
                    </a>
                </li>
            @endif

            @if ('permission:view_coupons')
                <li>
                    <a href="javascript:;" class="has-arrow">
                        <div class="parent-icon"><i class="material-icons-outlined">admin_panel_settings</i></div>
                        <div class="menu-title">Roles & Permissions</div>
                    </a>
                    <ul>
                        <li><a href="{{ route('admin.roles.index') }}"><i
                                    class="material-icons-outlined">arrow_right</i>Manage Roles</a>
                        </li>
                        <li><a href="{{ route('admin.permissions.index') }}"><i
                                    class="material-icons-outlined">arrow_right</i>Manage Permissions</a>
                        </li>
                        <li><a href="{{ route('admin.users.roles.index') }}"><i
                                    class="material-icons-outlined">arrow_right</i>User Roles</a>
                        </li>
                    </ul>
                </li>
            @endif

            @if ('permission:view_audit_logs')
                <li class="sidebar-item">
                    <a href="{{ route('admin.audi_logs.index') }}" class="sidebar-link">
                        <div class="parent-icon"><i class="material-icons-outlined">history</i></div>
                        <div class="menu-title"> AuditLogs</div>
                    </a>
                </li>
            @endif
        </ul>
    </div>
</aside>
