<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" data-bs-theme="blue-theme">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'Laravel') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Favicon -->
        <link rel="icon" href="{{ asset('assets/images/favicon-32x32.png') }}" type="image/png">

        <!-- Loader -->
        <link href="{{ asset('assets/css/pace.min.css') }}" rel="stylesheet">
        <script src="{{ asset('assets/js/pace.min.js') }}" defer></script>

        <!-- Plugins -->
        <link href="{{ asset('assets/plugins/perfect-scrollbar/css/perfect-scrollbar.css') }}" rel="stylesheet">
        <link href="{{ asset('assets/plugins/metismenu/metisMenu.min.css') }}" rel="stylesheet">
        <link href="{{ asset('assets/plugins/simplebar/css/simplebar.css') }}" rel="stylesheet">

        <!-- Bootstrap CSS -->
        <link href="{{ asset('assets/css/bootstrap.min.css') }}" rel="stylesheet">

        <!-- Google Fonts -->
        <link href="https://fonts.googleapis.com/css2?family=Noto+Sans:wght@300;400;500;600&display=swap" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css?family=Material+Icons+Outlined" rel="stylesheet">

        <!-- Main CSS -->
        <link href="{{ asset('assets/css/bootstrap-extended.css') }}" rel="stylesheet">
        <link href="{{ asset('sass/main.css') }}" rel="stylesheet">
        <link href="{{ asset('sass/dark-theme.css') }}" rel="stylesheet">
        <link href="{{ asset('sass/blue-theme.css') }}" rel="stylesheet">
        <link href="{{ asset('sass/semi-dark.css') }}" rel="stylesheet">
        <link href="{{ asset('sass/bordered-theme.css') }}" rel="stylesheet">
        <link href="{{ asset('sass/responsive.css') }}" rel="stylesheet">

        <!-- dataTables style -->

        @stack('plugin-styles')
        @stack('styles')
        <!-- jQuery -->
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
        @vite(['resources/css/app.css', 'resources/js/app.js'])
        <!-- Firebase Configuration -->
        <x-firebase-config />
    </head>
    <body class="font-sans antialiased ">
        @include('layouts.navigation')
        @include('layouts.sidebar')
        @include('layouts.switcher')
        @php
            $lang = session('notificationLang', 'ar');
        @endphp
        <x-confirm-delete-modal modalId="confirmDeleteModal" message="Are you sure you want to delete this item?" />

        <!-- Page Content -->
        <main class="main-wrapper">
            <div class="main-content">
                @yield('content')
            </div>
        </main>
        <!-- Bootstrap JS -->
        <script src="{{ asset('assets/js/bootstrap.bundle.min.js') }}" defer></script>

        <!-- Plugins JS -->

        <!-- <script src="{{ asset('assets/js/jquery.min.js') }}" defer></script>  -->
        <script src="{{ asset('assets/plugins/perfect-scrollbar/js/perfect-scrollbar.js') }}" defer></script>
        <script src="{{ asset('assets/plugins/metismenu/metisMenu.min.js') }}" defer></script>
        <script src="{{ asset('assets/plugins/simplebar/js/simplebar.min.js') }}" defer></script>
        <script src="{{ asset('assets/js/main.js') }}" defer></script>
        <script src="{{ asset('assets/js/admin/delete.js') }}" defer></script>
        <script src="{{ asset('assets/js/admin/utils.js') }}" defer></script>

                    @stack('data-table-scripts')
                    @stack('data-table-styles')
                    @stack('custom-scripts')
                    @stack('plugin-scripts')
                    @stack('scripts')
                    @stack('script')
                    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
                    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-database.js"></script>
                    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-messaging.js"></script>
                    <script src="{{ asset('js/fierbase.js') }}"></script>
                    @include("layouts.script.notification")

    </body>
</html>
