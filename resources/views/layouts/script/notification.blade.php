<script>
    $(document).ready(function() {
        $(document).on('click', '.notification-item', function() {
            var notificationId = $(this).data('id');
            $.ajax({
                type: "PUT",
                url: "/reade-notification",
                data: {
                    id: notificationId,
                    _token: '{{ csrf_token() }}'
                },
                success: function (response) {
                    var count = response.countUnreadMessages;
                    var badge = $('.badge-notify');
                    if (count > 0) {
                        badge.text(count).addClass('badge-notify');
                    } else {
                        badge.text('').removeClass('badge-notify');
                    }
                }
            });
        });
    });
</script>
