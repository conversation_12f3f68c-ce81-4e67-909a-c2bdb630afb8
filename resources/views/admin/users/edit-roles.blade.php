@extends('layouts.app')

@section('title', 'Edit User Roles - ' . $user->name)

@section('content')
    <div class="main-content">
        <div class="page-content">
            <div class="container-fluid">
                <!-- Page Title -->
                <div class="row">
                    <div class="col-12">
                        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                            <h4 class="mb-sm-0">Edit User Roles</h4>
                            <div class="page-title-right">
                                <ol class="breadcrumb m-0">
                                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                                    {{-- <li class="breadcrumb-item"><a href="{{ route('admin.users.roles') }}">User Roles</a></li> --}}
                                    <li class="breadcrumb-item active">Edit</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- User Information -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="material-icons-outlined me-2">person</i>
                                    User Information
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="text-center mb-3">
                                    @if ($user->avatar)
                                        <img src="{{ $user->avatar }}" class="rounded-circle" width="80" height="80"
                                            alt="Avatar">
                                    @else
                                        <div class="rounded-circle bg-secondary d-inline-flex align-items-center justify-content-center text-white"
                                            style="width: 80px; height: 80px; font-size: 2rem;">
                                            {{ strtoupper(substr($user->name, 0, 1)) }}
                                        </div>
                                    @endif
                                </div>

                                <div class="user-details">
                                    <div class="mb-3">
                                        <label class="form-label text-muted">Name</label>
                                        <p class="mb-0 fw-bold">{{ $user->name }}</p>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label text-muted">Email</label>
                                        <p class="mb-0">{{ $user->email }}</p>
                                    </div>

                                    @if ($user->phone)
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Phone</label>
                                            <p class="mb-0">{{ $user->phone }}</p>
                                        </div>
                                    @endif

                                    <div class="mb-3">
                                        <label class="form-label text-muted">Member Since</label>
                                        <p class="mb-0">{{ $user->created_at->format('M d, Y') }}</p>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label text-muted">Status</label>
                                        <p class="mb-0">
                                            @if ($user->email_verified_at)
                                                <span class="badge bg-success">Verified</span>
                                            @else
                                                <span class="badge bg-warning">Unverified</span>
                                            @endif
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Current Permissions Preview -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="material-icons-outlined me-2">security</i>
                                    Current Permissions
                                </h6>
                            </div>
                            <div class="card-body">
                                <div id="currentPermissions">
                                    <div class="text-center text-muted">
                                        <i class="material-icons-outlined" style="font-size: 3rem;">hourglass_empty</i>
                                        <p>Loading permissions...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Role Assignment -->
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="card-title mb-0">
                                    <i class="material-icons-outlined me-2">admin_panel_settings</i>
                                    Assign Roles to {{ $user->name }}
                                </h6>
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-outline-primary btn-sm" id="selectAllRoles">
                                        <i class="material-icons-outlined me-1">select_all</i>
                                        Select All
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm" id="deselectAllRoles">
                                        <i class="material-icons-outlined me-1">deselect</i>
                                        Deselect All
                                    </button>
                                    <a href="{{ route('admin.users.roles.index') }}" class="btn btn-secondary btn-sm">
                                        <i class="material-icons-outlined me-1">arrow_back</i>
                                        Back
                                    </a>
                                </div>
                            </div>
                            <div class="card-body">
                                <form id="userRolesForm">
                                    @csrf
                                    <div class="row">
                                        @foreach ($roles as $role)
                                            <div class="col-md-6 col-lg-4 mb-3">
                                                <div class="card border role-card" data-role="{{ $role->name }}">
                                                    <div class="card-body">
                                                        <div class="form-check">
                                                            <input class="form-check-input role-checkbox" type="checkbox"
                                                                name="roles[]" value="{{ $role->name }}"
                                                                id="role{{ $role->id }}"
                                                                {{ $user->hasRole($role->name) ? 'checked' : '' }}>
                                                            <label class="form-check-label fw-bold"
                                                                for="role{{ $role->id }}">
                                                                {{ ucfirst($role->name) }}
                                                            </label>
                                                        </div>

                                                        <div class="mt-2">
                                                            <small class="text-muted">
                                                                <i class="material-icons-outlined me-1"
                                                                    style="font-size: 14px;">people</i>
                                                                {{ $role->users()->count() }} users
                                                            </small>
                                                            <br>
                                                            <small class="text-muted">
                                                                <i class="material-icons-outlined me-1"
                                                                    style="font-size: 14px;">security</i>
                                                                {{ $role->permissions()->count() }} permissions
                                                            </small>
                                                        </div>

                                                        @if ($role->permissions()->count() > 0)
                                                            <div class="mt-2">
                                                                <button type="button"
                                                                    class="btn btn-outline-info btn-sm view-permissions"
                                                                    data-role="{{ $role->name }}">
                                                                    <i class="material-icons-outlined me-1"
                                                                        style="font-size: 14px;">visibility</i>
                                                                    View Permissions
                                                                </button>
                                                            </div>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>

                                    <div class="row mt-4">
                                        <div class="col-12">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div class="text-muted">
                                                    <small>
                                                        <i class="material-icons-outlined me-1"
                                                            style="font-size: 16px;">info</i>
                                                        Changes will be applied immediately after saving
                                                    </small>
                                                </div>
                                                <div>
                                                    <button type="submit" class="btn btn-primary">
                                                        <i class="material-icons-outlined me-1">save</i>
                                                        Save Role Changes
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Role Permissions Modal -->
    <div class="modal fade" id="rolePermissionsModal" tabindex="-1" aria-labelledby="rolePermissionsModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="rolePermissionsModalLabel">
                        <i class="material-icons-outlined me-2">security</i>
                        Role Permissions
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="rolePermissionsContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
    <style>
        .role-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .role-card:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .role-card.selected {
            border-color: #007bff;
            background-color: #f8f9ff;
        }

        .form-check-input:checked {
            background-color: #007bff;
            border-color: #007bff;
        }

        .user-details .form-label {
            font-size: 0.875rem;
            font-weight: 600;
        }

        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
    </style>
@endpush

@push('plugin-scripts')
    <script>
        $(document).ready(function() {
            // Select All Roles
            $('#selectAllRoles').on('click', function() {
                $('.role-checkbox').prop('checked', true);
                updateRoleCards();
                updatePermissionsPreview();
            });

            // Deselect All Roles
            $('#deselectAllRoles').on('click', function() {
                $('.role-checkbox').prop('checked', false);
                updateRoleCards();
                updatePermissionsPreview();
            });

            // Role checkbox change
            $('.role-checkbox').on('change', function() {
                updateRoleCards();
                updatePermissionsPreview();
            });

            // Role card click (toggle checkbox)
            $('.role-card').on('click', function(e) {
                if (!$(e.target).is('input, button, .btn')) {
                    const checkbox = $(this).find('.role-checkbox');
                    checkbox.prop('checked', !checkbox.prop('checked'));
                    updateRoleCards();
                    updatePermissionsPreview();
                }
            });

            // Update role card appearance
            function updateRoleCards() {
                $('.role-card').each(function() {
                    const checkbox = $(this).find('.role-checkbox');
                    if (checkbox.is(':checked')) {
                        $(this).addClass('selected');
                    } else {
                        $(this).removeClass('selected');
                    }
                });
            }

            // View role permissions
            $('.view-permissions').on('click', function(e) {
                e.stopPropagation();
                const roleName = $(this).data('role');

                $('#rolePermissionsModalLabel').html(
                    `<i class="material-icons-outlined me-2">security</i>Permissions for "${roleName}" Role`
                    );
                $('#rolePermissionsContent').html(
                    '<div class="text-center"><i class="material-icons-outlined" style="font-size: 3rem;">hourglass_empty</i><p>Loading permissions...</p></div>'
                    );

                $('#rolePermissionsModal').modal('show');

                // Load permissions via AJAX
                $.get(`/dashboard/admin/roles/${roleName}/permissions-preview`)
                    .done(function(data) {
                        $('#rolePermissionsContent').html(data);
                    })
                    .fail(function() {
                        $('#rolePermissionsContent').html(
                            '<div class="alert alert-danger">Failed to load permissions.</div>');
                    });
            });

            // Update permissions preview
            function updatePermissionsPreview() {
                const selectedRoles = $('.role-checkbox:checked').map(function() {
                    return $(this).val();
                }).get();

                if (selectedRoles.length === 0) {
                    $('#currentPermissions').html(
                        '<div class="text-center text-muted"><i class="material-icons-outlined" style="font-size: 3rem;">block</i><p>No roles selected</p></div>'
                        );
                    return;
                }

                $('#currentPermissions').html(
                    '<div class="text-center text-muted"><i class="material-icons-outlined" style="font-size: 3rem;">hourglass_empty</i><p>Loading permissions...</p></div>'
                    );

                $.post('{{ route('admin.users.roles.preview-permissions') }}', {
                        _token: '{{ csrf_token() }}',
                        roles: selectedRoles
                    })
                    .done(function(data) {
                        $('#currentPermissions').html(data);
                    })
                    .fail(function() {
                        $('#currentPermissions').html(
                            '<div class="alert alert-danger">Failed to load permissions preview.</div>');
                    });
            }

            // Form submission
            $('#userRolesForm').on('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);

                // Show loading state
                const submitBtn = $(this).find('button[type="submit"]');
                const originalText = submitBtn.html();
                submitBtn.html('<i class="material-icons-outlined me-1">hourglass_empty</i>Saving...').prop(
                    'disabled', true);

                $.ajax({
                    url: '{{ route('admin.users.roles.update', $user->id) }}',
                    method: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Success!',
                                text: response.message,
                                timer: 3000,
                                showConfirmButton: false
                            }).then(() => {
                                // Optionally redirect back to user roles list
                                window.location.href =
                                    '{{ route('admin.users.roles.index') }}';
                            });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error!',
                                text: response.message ||
                                    'An error occurred while updating user roles.'
                            });
                        }
                    },
                    error: function(xhr) {
                        console.error('Error:', xhr);
                        Swal.fire({
                            icon: 'error',
                            title: 'Error!',
                            text: 'An error occurred while updating user roles.'
                        });
                    },
                    complete: function() {
                        // Restore button state
                        submitBtn.html(originalText).prop('disabled', false);
                    }
                });
            });

            // Initialize
            updateRoleCards();
            updatePermissionsPreview();
        });
    </script>
@endpush
