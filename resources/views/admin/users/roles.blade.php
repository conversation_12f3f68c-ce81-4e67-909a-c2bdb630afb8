@push('plugin-scripts')
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.responsive.min.js"></script>
@endpush

@push('plugin-styles')
    <link href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css" rel="stylesheet" />
    <link href="https://cdn.datatables.net/1.11.5/css/responsive.dataTables.min.css" rel="stylesheet" />
@endpush

@extends('layouts.app')

@section('content')

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-left-primary">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-muted mb-1">Total Users</h6>
                        <h4 class="mb-0" id="totalUsersCount">-</h4>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="material-icons-outlined text-primary" style="font-size: 2rem;">people</i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-success">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-muted mb-1">With Roles</h6>
                        <h4 class="mb-0" id="usersWithRolesCount">-</h4>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="material-icons-outlined text-success" style="font-size: 2rem;">assignment_ind</i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-warning">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-muted mb-1">Without Roles</h6>
                        <h4 class="mb-0" id="usersWithoutRolesCount">-</h4>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="material-icons-outlined text-warning" style="font-size: 2rem;">person_outline</i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-info">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-muted mb-1">Available Roles</h6>
                        <h4 class="mb-0" id="availableRolesCount">-</h4>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="material-icons-outlined text-info" style="font-size: 2rem;">admin_panel_settings</i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filter and Action Buttons -->
<div class="d-flex justify-content-between align-items-center mb-3">
    <div class="dropdown">
        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
            <i class="material-icons-outlined me-1">filter_list</i>
            Filter by Role
        </button>
        <ul class="dropdown-menu" id="roleFilterDropdown">
            <li><a class="dropdown-item role-filter" href="#" data-role="">All Users</a></li>
            <li><hr class="dropdown-divider"></li>
            <!-- Roles will be populated here -->
        </ul>
    </div>
    <a href="{{ route('admin.roles.index') }}" class="btn btn-info">
        <i class="material-icons-outlined me-1">admin_panel_settings</i>
        Manage Roles
    </a>
</div>

<!-- Table with Bootstrap and custom styles -->
<div class="table-responsive">
    <x-data-table
        title="User Role Assignments"
        table-id="user-roles-table"
        fetch-url="{{ route('admin.users.roles.data') }}"
        :columns="['ID', 'User', 'Email', 'Current Roles', 'Last Login', 'Actions']"
        :columns-config="[
            ['data' => 'id', 'name' => 'id'],
            ['data' => 'name', 'name' => 'name'],
            ['data' => 'email', 'name' => 'email'],
            ['data' => 'roles', 'name' => 'roles', 'orderable' => false, 'searchable' => false],
            ['data' => 'updated_at', 'name' => 'updated_at' ],
            ['data' => 'actions', 'name' => 'actions', 'orderable' => false, 'searchable' => false]
        ]"
    />
</div>
@endsection

@push('data-table-styles')
<style>
    .border-left-primary {
        border-left: 4px solid #007bff;
    }

    .border-left-success {
        border-left: 4px solid #28a745;
    }

    .border-left-info {
        border-left: 4px solid #17a2b8;
    }

    .border-left-warning {
        border-left: 4px solid #ffc107;
    }

    .user-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        object-fit: cover;
    }

    .role-badge {
        font-size: 0.75em;
        margin-right: 0.25rem;
        margin-bottom: 0.25rem;
    }
</style>
@endpush

@push('data-table-scripts')
<script>
$(document).ready(function() {
    let currentRoleFilter = '';

    // Wait for the DataTable to be ready
    $(document).on('TableReady', function() {
        const userRolesTable = window['userRolesTable'];

        // Update summary cards and filter dropdown when data loads
        userRolesTable.on('xhr.dt', function(e, settings, json, xhr) {
            updateSummaryCards(json);
            updateRoleFilterDropdown(json);
        });

        // Load initial data
        userRolesTable.ajax.reload();

        // Role filter functionality
        $(document).on('click', '.role-filter', function(e) {
            e.preventDefault();
            currentRoleFilter = $(this).data('role');

            // Update button text
            const filterText = currentRoleFilter ? `Filter: ${currentRoleFilter}` : 'Filter by Role';
            $('.dropdown-toggle').html(`<i class="material-icons-outlined me-1">filter_list</i>${filterText}`);

            // Update DataTable settings to include filter
            userRolesTable.settings()[0].ajax.data = function(d) {
                d.role_filter = currentRoleFilter;
                return d;
            };

            // Reload table
            userRolesTable.ajax.reload();
        });
    });

    // Get role badge class based on role name
    function getRoleBadgeClass(role) {
        const roleClasses = {
            'admin': 'bg-danger',
            'manager': 'bg-warning',
            'editor': 'bg-info',
            'user': 'bg-secondary',
            'driver': 'bg-success',
            'customer': 'bg-primary'
        };

        return roleClasses[role.toLowerCase()] || 'bg-secondary';
    }

    // Update summary cards
    function updateSummaryCards(data) {
        if (data.summary) {
            $('#totalUsersCount').text(data.summary.total_users || '-');
            $('#usersWithRolesCount').text(data.summary.users_with_roles || '-');
            $('#usersWithoutRolesCount').text(data.summary.users_without_roles || '-');
            $('#availableRolesCount').text(data.summary.available_roles || '-');
        }
    }

    // Update role filter dropdown
    function updateRoleFilterDropdown(data) {
        if (data.roles) {
            const dropdown = $('#roleFilterDropdown');
            // Keep the "All Users" option and divider
            const staticItems = dropdown.find('li').slice(0, 2);
            dropdown.empty().append(staticItems);

            // Add role options
            data.roles.forEach(function(role) {
                dropdown.append(`<li><a class="dropdown-item role-filter" href="#" data-role="${role.name}">${role.name} (${role.users_count})</a></li>`);
            });
        }
    }
});
</script>
@endpush
